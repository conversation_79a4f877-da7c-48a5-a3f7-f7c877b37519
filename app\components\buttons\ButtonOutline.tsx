import React from "react";

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
}

const ButtonOutline: React.FC<ButtonProps> = ({
  children,
  onClick,
  icon,
  iconPosition = "left",
}) => {
  return (
    <button
      onClick={onClick}
      className="flex items-center gap-2 px-4 py-2 border border-primaryVersion2 text-primaryVersion2 rounded-md hover:bg-primaryVersion2 hover:text-black transition-colors duration-300"
    >
      {icon && iconPosition === "left" && <span>{icon}</span>}
      <span>{children}</span>
      {icon && iconPosition === "right" && <span>{icon}</span>}
    </button>
  );
};

export default ButtonOutline;
