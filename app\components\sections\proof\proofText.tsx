// components/ProofText.tsx
import React from "react";
import { FaCheckCircle } from "react-icons/fa";
import SectionHeading from "./SectionHeading";

const ProofText: React.FC = () => {
  return (
    <div className="w-full bg-black z-30 flex-1 flex flex-col gap-6 text-white relative px-6">
      <div className="w-full ipad-mini:mt-10 ipad-mini:max-w-xl mx-auto">
        <SectionHeading  className="hidden lg:block" />

        <div className="my-4 md:my-8 space-y-2 md:space-y-4 text-white/50 text-base md:text-xl font-light leading-relaxed ">
          {[
            "Tanpa pandai bahasa inggris",
            "Tanpa menggunakan AI berbayar",
            "dan <PERSON>pa menulis prompt"
          ].map((text, idx) => (
            <div key={idx} className="flex items-center gap-3">
              <FaCheckCircle className="w-5 md:w-6 h-5 md:h-6 text-primaryVersion2" />
              <span>{text}</span>
            </div>
          ))}
        </div>

        <div className=" text-xl md:text-3xl font-bold text-primaryVersion2 italic">
          Kok bisa?
        </div>

        <p className="mt-4 md:mt-8 text-white/50 text-base md:text-xl font-light leading-relaxed">
          Bisa dong, ini buktinya…
        </p>
        <p className="text-white/50 text-base md:text-xl font-light leading-relaxed mb-8">
          semua ini dibikin tanpa menulis prompt dan cuma pake AI yang gratisan
        </p>
      </div>      
    </div>

  );
};

export default ProofText;
