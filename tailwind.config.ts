import type { Config } from "tailwindcss";

const config: Config = {
	darkMode: "class",
	content: ["./app/**/*.{js,ts,jsx,tsx,mdx}", "./components/**/*.{js,ts,jsx,tsx}"],
	theme: {
		extend: {
			screens: {
				'ipad': { 'raw': '(min-device-width: 768px) and (max-device-width: 1024px)' },
				'ipad-mini': { min: "768px", max: "1023px" },
				'nest-hub': { raw: '(device-width: 1024px) and (device-height: 600px)' },
				'nest-hub-max': { raw: '(device-width: 1280px) and (device-height: 800px)' },
			},
			boxShadow: {
				'inset-down': 'inset 0 -10px 10px rgba(0, 0, 0, 0.5)'
			},
			colors: {
				secondary: '#CE6F15',
				primaryYellow: '#F8BC24',
				primary: '#E5A10E',
				primaryVersion2: '#E4A501',
				light: '#F7E7CD',
				dark: '#643521',
					'dark-2': '#222020',
					'dark-3': '#615C5C',
					'dark-4': '#0B0A08',
				accent: '#D92137',
				font: '#1A1A1A'
			},
			backgroundImage: {
				'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
				'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
				'custom-gradient': 'linear-gradient(160deg, #e5a10e 20%, #f7e7cd 100%)',
				'custom-gradient-v2': 'linear-gradient(160deg, #f7e7cd -25%, #e5a10e 36%,#ce6f15 110%)',
				'gradient-soft-up': 'linear-gradient(to top, rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0))'
			},
			fontFamily: {
				'space-grotesk': [
					'Space Grotesk',
					'sans-serif'
				],
				philosopher: [
					'Philosopher',
					'serif'
				],
				poppins: [
					'Poppins',
					'sans-serif'
				],
				'open-sans': [
					'Open Sans', 
					'sans-serif'
				],
				nunito: [
					'Nunito', 
					'sans-serif'
				],
			},
			keyframes: {
				fadeIn: {
					'0%': {
						opacity: '0'
					},
					'100%': {
						opacity: '1'
					}
				},
				marquee: {
					'0%': {
						transform: 'translateX(100%)'
					},
					'100%': {
						transform: 'translateX(-105%)'
					}
				}, 
				dropFade: {
					'0%': {
						transform: 'translateY(0)',
						opacity: '1',	
					},
					'50%': {
						transform: 'translateY(140%)',
						opacity: '0.5',
					},
					'100%': {
						transform: 'translateY(120%)',
						opacity: '0',
					},
				},
				riseFade: {
					'0%': {
						transform: 'translateY(0)',
						opacity: '0',
					},
					'100%': {
						transform: 'translateY(-160%)',
						opacity: '1',
					},
				},
			},
			animation: {
				fadeIn: 'fadeIn 1s ease-out',
				marquee: 'marquee 10s linear infinite',
				dropFade: 'dropFade 3.4s ease forwards',
				riseFade: 'riseFade 2s ease forwards',
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			}
		}
	},
  	plugins: [require("@tailwindcss/typography"), require("tailwindcss-animate"), require('tailwind-scrollbar-hide'),  require("tailwind-scrollbar")],
};
export default config;
