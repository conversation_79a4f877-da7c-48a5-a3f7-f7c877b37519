import React from "react";
import Image from "next/image";
import TiltedCard from "../../TiltedCard";

const classOptions = [
  {
    title: "<PERSON><PERSON>",
    age: "Untuk usia 13–23 tahun",
    description:
      "Belajar langsung secara offline dengan pendekatan interaktif dan menyenangkan.",
  },
  {
    title: "Kelas Umum",
    age: "Untuk usia 24 tahun ke atas",
    description:
      "Tersedia dalam dua pilihan: kelas offline dan kelas online yang fleksibel.",
  },
];

const ClassSelection: React.FC = () => {
  return (
    <section className="w-full px-6 bg-black py-16 text-white">
      <div className="w-full ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto text-center">
        <h2 className="text-center text-2xl md:text-4xl font-semibold mb-12 text-white  px-6 sm:px-10">
          <PERSON><PERSON><PERSON> <span className="text-primaryVersion2 font-bold">GAMPAR AI</span>
        </h2>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Left image */}
          <div className="flex justify-center items-center w-full lg:w-1/2 aspect-square lg:h-[600px]">
            <TiltedCard
              imageSrc="/images/accessories/home-card-3.webp"
              altText="Aurum - GNX Album Cover"
              captionText="Aurum - AI Influencer"
              containerHeight="100%"
              containerWidth="100%"
              imageHeight="100%"
              imageWidth="100%"
              rotateAmplitude={12}
              scaleOnHover={1}
              showMobileWarning={false}
              showTooltip={true}
              displayOverlayContent={true}
              />
          </div>

          {/* Right cards */}
          <div className="w-full lg:w-1/2 lg:h-[600px] flex flex-col gap-6">
            {classOptions.map((item, index) => (
              <div
                key={index}
                className="flex-1 bg-dark-2 border border-white/10 backdrop-blur-md p-6 rounded-xl flex flex-col justify-center"
              >
                <h3 className="text-white text-base lg:text-xl font-semibold mb-2 md:mb-8">{item.title}</h3>
                <p className="text-white/70 text-xs md:text-base mb-2 md:mb-8">{item.age}</p>
                <p className="text-white/60 text-xs md:text-base italic">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClassSelection;
