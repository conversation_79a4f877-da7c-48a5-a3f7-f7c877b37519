"use client";
import React from "react";
import MapIframe from "../../shared/MapIframe";

const MapsGamparAI: React.FC = () => {
  const redirectToMaps = () => {
    window.open("https://maps.app.goo.gl/1bvXevbz7KaUBMEm9", "_blank");
  };

  return (
    <section className="w-full px-6 bg-black py-16 text-white">
      <div className="w-full ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto text-center">
        <div className="flex flex-col ipad-mini:flex-col md:flex-row justify-between gap-4 md:gap-10">
          {/* Deskripsi */}
          <div className="w-full ipad-mini:w-full md:w-1/3 flex flex-col justify-between">
            <h2 className="text-2xl md:text-3xl font-semibold mb-8 text-white">
              Lokasi <PERSON>las Offline
            </h2>
            <p className="text-sm md:text-base font-light leading-relaxed text-white/50 tracking-widest">
              <PERSON><PERSON><PERSON>, <PERSON><PERSON>k A8/11 Kecamatan Cikancung, Kabupaten Bandung Jawa Barat 40396
            </p>
          </div>

          {/* Iframe Container with Absolute Button */}
          <div className="relative w-full ipad-mini:w-full md:w-[665px] aspect-video mt-8 lg:mt-24">
            {/* Button absolute */}
            <button
              onClick={redirectToMaps}
              className="hidden md:flex absolute top-4 right-4 z-20 bg-primaryVersion2 hover:bg-secondary text-black font-semibold px-4 py-2 rounded-md transition"
            >
              Buka di Google Maps
            </button>
            <MapIframe />
          </div>

          <button
              onClick={redirectToMaps}
              className="flex justify-center md:hidden bg-primaryVersion2 hover:bg-secondary text-black font-semibold px-4 py-2 rounded-md transition"
            >
            Buka di Google Maps
          </button>
        </div>
      </div>
    </section>
  );
};

export default MapsGamparAI;
