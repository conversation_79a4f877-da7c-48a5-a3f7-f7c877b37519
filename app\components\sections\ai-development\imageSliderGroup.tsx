"use client";
import React from "react";
import ImageSliderRow from "./imageSliderRow";

const imageRows = [
  ["/images/cards/section-2/z2.webp", "/images/cards/section-2/z5.webp", "/images/cards/section-2/z6.webp", "/images/cards/section-2/z7.webp", "/images/cards/section-2/z8.webp"],
  ["/images/cards/section-2/y1.webp", "/images/cards/section-2/y4.webp", "/images/cards/section-2/y5.webp", "/images/cards/section-2/y6.webp", "/images/cards/section-2/y8.webp"],
  ["/images/cards/section-2/w1.webp", "/images/cards/section-2/w2.webp", "/images/cards/section-2/w3.webp", "/images/cards/section-2/w4.webp", "/images/cards/section-2/w5.webp"],
];

const ImageSliderGroup: React.FC = () => {
  return (
    <div className="flex-1 w-full md:w-1/2 flex flex-col gap-2 max-w-xl">
      {imageRows.map((row, rowIndex) => (
        <ImageSliderRow key={rowIndex} images={row} reversed={rowIndex % 2 !== 0} />
      ))}
    </div>
  );
};

export default ImageSliderGroup;
