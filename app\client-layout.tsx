"use client";
import { ReactNode } from "react";
import { usePathname } from "next/navigation";
import NavbarMenu from "./components/NavbarMenu";
import Footer from "./components/Footer";

const NAVBAR_PATHS = ["/", "/umum", "/pelajar", "/tentang-kami", "/kontak"];

const shouldShowNavbar = (path: string): boolean => {
  return (
    NAVBAR_PATHS.includes(path) 
  );
};

const shouldShowSections = (path: string): boolean => {
  return NAVBAR_PATHS.includes(path);
};

export default function ClientLayout({ children }: { children: ReactNode }) {
  const currentPath = usePathname();

  return (
    <>
      {shouldShowNavbar(currentPath) && <NavbarMenu />}
      <main>{children}</main>
      {shouldShowSections(currentPath) && <Footer/>}
    </>
  );
}