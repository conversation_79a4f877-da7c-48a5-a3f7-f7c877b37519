import React from "react";
import { Metadata } from "next";
import HeroPelajarSection from "../components/sections/hero-pelajar";
import InvestmentsSection from "./investment";
import BenefitsSection from "./benefits";
import LearnedSkillsSection from "./LearnedSkillsSection";
import LearningMethodSection from "./LearningMethodSection";
import CertificateBonusSection from "./certificateBonusSection";
import MapsSection from "./maps";
import PricingSection from "./pricing";
import Link from "next/link";
import { FaWhatsapp } from "react-icons/fa";
import ProfileMentorSection from "./mentors";
import RequirementsSection from "./requirements";
import { faqsPelajar } from "../datas/faq";
import FAQSection from "../components/sections/FAQSection";
import BannerSections from "./banner";


export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3030"),
  title: "Aigensee - Garisprtama - Gampar AI",
  description: "Aigensee - Kursus Online AI and Animation.",
  openGraph: {
    title: "Aigensee - Kursus Online AI and Animation",
    description: "Aigensee - Kursus Online AI and Animation.",
    url: "/",
    images: ["/images/accessories/Aigensee-Logo.png"],
  },
};
const LandingPelajarPage: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center mt-10 lg:mx-auto relative overflow-x-hidden">
      <HeroPelajarSection/>
      <InvestmentsSection/>
      <BenefitsSection/>
      <LearnedSkillsSection/>
      <LearningMethodSection/>
      <CertificateBonusSection/>
      <MapsSection/>
      <PricingSection/>
      <section className="w-full bg-black text-white py-20 px-6">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-2xl md:text-3xl font-bold mb-12">
            Daftar Sekarang dan Ambil Promonya
          </h2>
          <Link
            href="https://wa.me/6289519442003?text=Halo%20admin%2C%20saya%20mau%20tanya%20tentang%20program%20Pelajar"
            target="_blank"
            rel="noopener noreferrer" className="inline-flex items-center justify-center gap-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold text-base md:text-2xl transition duration-300 mx-auto"   
          >
            <FaWhatsapp  className=" text-2xl md:text-4xl"/>
            Whatsapp
          </Link>            
        </div>
      </section>
      <RequirementsSection />
      <ProfileMentorSection />
      <div className="ipad-mini:max-w-xl ipad:max-w-2xl lg:max-w-3xl mx-auto text-white">
        <h2 className="text-2xl md:text-3xl font-bold md:text-center mb-8">
          Paling Sering Ditanyakan
        </h2>
      </div>
      <FAQSection faqs={faqsPelajar} />
      <BannerSections />
    </div>
  );
};

export default LandingPelajarPage;