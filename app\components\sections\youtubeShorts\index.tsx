"use client";
import React, { useRef, useState } from "react";
import Slider from "react-slick";
import { FaPlay } from "react-icons/fa";

const shorts = [
  "BH-wk5eDy2M",
  "BH-wk5eDy2M",
  "BH-wk5eDy2M",
];

type YoutubeShortCardProps = {
  videoId: string;
  isPlaying: boolean;
  onPlay: () => void;
  iframeRef: (el: HTMLIFrameElement | null) => void;
};

const YoutubeShortCard: React.FC<YoutubeShortCardProps> = ({
  videoId,
  isPlaying,
  onPlay,
  iframeRef,
}) => {
  return (
    <div className="relative bg-neutral-800 rounded-xl overflow-hidden group aspect-[9/16] px-2">
      <iframe
        ref={iframeRef}
        src={`https://www.youtube.com/embed/${videoId}?enablejsapi=1&modestbranding=1&rel=0`}
        className="w-full h-full absolute top-0 left-0"
        allow="autoplay; encrypted-media"
        allowFullScreen
        title={`YouTube Short ${videoId}`}
      ></iframe>

      {!isPlaying && (
        <button
          onClick={onPlay}
          className="hidden md:flex absolute inset-0 items-center justify-center bg-black/50 hover:bg-black/30 transition-colors z-10"
        >
          <div className="bg-yellow-500 rounded-full p-4">
            <FaPlay className="text-black text-lg" />
          </div>
        </button>
      )}
    </div>
  );
};

const YoutubeShortsSection: React.FC = () => {
  const iframeRefs = useRef<(HTMLIFrameElement | null)[]>([]);
  const [playingIndex, setPlayingIndex] = useState<number | null>(null);

  const stopAllVideos = () => {
    iframeRefs.current.forEach((iframe) => {
      iframe?.contentWindow?.postMessage(
        JSON.stringify({
          event: "command",
          func: "pauseVideo",
          args: [],
        }),
        "*"
      );
    });
    setPlayingIndex(null);
  };

  const handlePlay = (index: number) => {
    stopAllVideos();
    setPlayingIndex(index);
    iframeRefs.current[index]?.contentWindow?.postMessage(
      JSON.stringify({
        event: "command",
        func: "playVideo",
        args: [],
      }),
      "*"
    );
  };

  const sliderSettings = {
    infinite: false,
    autoplay: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    afterChange: () => stopAllVideos(),
    beforeChange: () => stopAllVideos(),
  };

  return (
    <section className="z-30 w-full py-16 px-6 bg-black">
      {/* Mobile Slider */}
      <div className="block md:hidden">
        <Slider {...sliderSettings}>
          {shorts.map((id, index) => (
            <YoutubeShortCard
              key={index}
              videoId={id}
              isPlaying={playingIndex === index}
              onPlay={() => handlePlay(index)}
              iframeRef={(el) => (iframeRefs.current[index] = el)}
            />
          ))}
        </Slider>
      </div>

      {/* Grid Layout for Tablet & Desktop */}
      <div className="hidden md:grid ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto grid-cols-2 md:grid-cols-3 gap-6">
        {shorts.map((id, index) => (
          <YoutubeShortCard
            key={index}
            videoId={id}
            isPlaying={playingIndex === index}
            onPlay={() => handlePlay(index)}
            iframeRef={(el) => (iframeRefs.current[index] = el)}
          />
        ))}
      </div>
    </section>
  );
};

export default YoutubeShortsSection;
