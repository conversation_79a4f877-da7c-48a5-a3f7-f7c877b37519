"use client";
import React from "react";

const LearningMethodSection: React.FC = () => {
  return (
    <section className="w-full bg-black text-white py-20 px-6">
      <div className="max-w-7xl mx-auto text-center">
        <h2 className="text-2xl md:text-3xl font-semibold mb-12">
          Metode Belajar
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Kelas */}
          <div className="bg-dark-2 rounded-2xl overflow-hidden">
            <div className="bg-primaryVersion2 text-black text-xl font-bold py-3">
              Kelas
            </div>
            <div className="px-6 py-8 md:px-20 md:py-14 text-left text-white/50 text-xs md:text-xl">
              <p className="font-medium text-white mb-6">
                Offline Private Class (Tatap Muka <PERSON>sun<PERSON>)
              </p>
              <p>
                Belajar langsung dengan mentor be<PERSON><PERSON><PERSON><PERSON>, agar proses belajar lebih fokus, terarah, dan optimal.
              </p>
            </div>
          </div>

          {/* Durasi */}
          <div className="bg-dark-2 rounded-2xl overflow-hidden">
            <div className="bg-primaryVersion2 text-black text-xl font-bold py-3">
              Durasi
            </div>
            <div className="px-6 py-8 md:px-20 md:py-14 text-left text-white/50 text-xs md:text-xl">
              <p className="font-medium text-white mb-6">6 Jam total.</p>
              <p className="mb-2">
                Dibagi menjadi <span className="text-primaryVersion2 font-semibold">4 sesi × 90 menit</span>. Bisa dijadwalkan <span className="text-primaryVersion2 font-semibold">1 sesi per minggu</span>. Pilihan Jam Belajar:
              </p>
              <ul className="list-disc list-inside space-y-1">
                <li>13:00 – 14:30</li>
                <li>14:30 – 16:00</li>
                <li>16:00 – 17:30</li>
                <li>18:30 – 20:00</li>
              </ul>
              <p className="mt-2">
                Jadwal fleksibel, bisa dipilih sesuai waktu Putra-Putri Anda.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LearningMethodSection;
