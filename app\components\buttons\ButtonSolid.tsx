import React from "react";

interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
  className?: string;
}

const ButtonSolid: React.FC<ButtonProps> = ({
  children,
  onClick,
  icon,
  iconPosition = "left",
  className,
}) => {
  return (
    <button
      onClick={onClick}
      className={`flex w-full text-center justify-center items-center gap-2 px-4 py-2 bg-primaryVersion2 text-black rounded-md hover:bg-yellow-600 transition-colors duration-300 ${className}`}
    >
      {icon && iconPosition === "left" && <span>{icon}</span>}
      <span>{children}</span>
      {icon && iconPosition === "right" && <span>{icon}</span>}
    </button>
  );
};

export default ButtonSolid;
