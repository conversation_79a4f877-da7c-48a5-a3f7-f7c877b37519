"use client";
import React from "react";
import Image from "next/image";

const benefits = [
  {
    title: "Teknologi AI sudah di sekitar kita",
    description:
      "Di media sosial, di aplikasi belajar, di konten yang mereka lihat setiap hari. <PERSON><PERSON><PERSON> saat ini, kita sudah mulai sulit membedakan mana gambar asli dan mana buatan AI — dan ini baru tahun 2025.",
    image: "/images/cards/section-1/th1.webp",
  },
  {
    title: "Bayangkan tahun 2026… 2030… 2040?",
    description:
      "Putra-putri kita akan tumbuh di dunia yang sepenuhnya didukung AI. <PERSON>an hanya jadi penonton perubahan — bantu putra-putri Anda jadi bagian dari kemajuan itu sendiri.",
    image: "/images/cards/section-1/th2.webp",
  },
];

const BenefitsSection: React.FC = () => {
  return (
    <section className="w-full bg-black pt-16 pb-64 px-6 text-white">
      <div className="max-w-6xl mx-auto text-center">
        <h2 className="text-2xl md:text-3xl font-semibold mb-12">
          Kenapa Putra-Putri Anda Perlu Belajar AI?
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 md:px-8 gap-56 md:gap-8">
          {benefits.map((benefit, idx) => (
            <div
              key={idx}
              className="relative rounded-2xl transition duration-300 aspect-[4/3] w-full"
            >
              {/* Background Image */}
              <Image
                src={benefit.image}
                alt={benefit.title}
                fill
                className="object-cover object-top rounded-2xl"
              />
              {/* Text Content */}
              <div className="w-[90%] ipad-mini:w-[90%] md:w-3/4 absolute -bottom-36 ipad-mini:-bottom-28 md:-bottom-48 left-1/2 -translate-x-1/2 z-20 bg-dark-2 p-6 ipad-mini:p-6 md:p-10 rounded-xl h-[200px] ipad-mini:h-[150px] md:h-[280px] flex flex-col justify-center items-start text-left">
                <h4 className="text-lg ipad-mini:text-sm md:text-xl font-semibold mb-6">
                  {benefit.title}
                </h4>
                <p className="text-xs ipad-mini:text-xs md:text-base text-white/80">
                  {benefit.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;