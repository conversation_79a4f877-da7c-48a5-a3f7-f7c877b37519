import React from "react";

const benefits = [
  {
    title: "Mudah Dipahami",
    subtitle: "Semua <PERSON>",
    description:
      "Metode belajar kami tidak rumit dan sangat praktis. Kamu bisa langsung mempraktikkan tanpa harus pusing dengan istilah teknis.",
  },
  {
    title: "Belajar",
    subtitle: "Sambil Berkarya",
    description:
      "Tidak hanya tentang teori. Kamu akan bisa membuat karya visual seperti ilustrasi, poster, bahkan konten media sosial tanpa proses yang rumit.",
  },
];

const BenefitGamparAI: React.FC = () => {
  return (
    <section className="w-full px-6 bg-black py-16 text-white">
      <div className="w-full ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto text-center">
        <h2 className="text-center text-3xl md:text-4xl font-semibold mb-12 text-white px-6 sm:px-10">
          Kenapa Harus
          <span className="text-primaryVersion2 font-bold"> GAMPAR AI</span>
        </h2>

        <div className="flex flex-col md:flex-row items-stretch justify-center gap-6">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="border border-white/20 rounded-xl lg:max-w-sm w-full p-6 backdrop-blur-sm flex flex-col justify-between"
            >
              <div>
                <p className="flex flex-col text-base md:text-xl text-white mb-4 font-bold">
                  <span>{benefit.title}</span>
                  <span>{benefit.subtitle}</span>
                </p>
                <p className="text-xs  md:text-base text-white/50 font-light tracking-widest">
                  {benefit.description}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BenefitGamparAI;
