"use client";
import React, { useState, useRef } from "react";
import Slider from "react-slick";
import Image from "next/image";
import { ChevronLeft, ChevronRight, X } from "lucide-react";
import { BsPlayFill } from "react-icons/bs";

const testimonials = [
  { thumbnail: "/images/cards/testimonials/testimoni-andre.png", videoId: "mtYCjZNq1Rw" },
  { thumbnail: "/images/cards/testimonials/testimoni-aisyah.png", videoId: "XlkwB0Z8qkg" },
  { thumbnail: "/images/cards/testimonials/testimoni-dira.png", videoId: "LNN1aEJxQwU" },
  { thumbnail: "/images/cards/testimonials/testimoni-nurul.png", videoId: "iVvwJ4rvW4E" },
  { thumbnail: "/images/cards/testimonials/testimoni-zahra.png", videoId: "ASADdthL8Q0" },
  { thumbnail: "/images/cards/testimonials/testimoni-wanda.png", videoId: "LGB7zeHztTE" },
];

const TestimonialSection = () => {
  const [selectedVideoId, setSelectedVideoId] = useState<string | null>(null);
  const sliderRef = useRef<Slider | null>(null);

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    autoplay: true,
    autoplaySpeed: 3000,
    slidesToShow: 3,
    slidesToScroll: 1,
    arrows: false,
    responsive: [
      {
        breakpoint: 1280,
        settings: {
          slidesToShow: 3,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          centerMode: true,
          centerPadding: "1%",
        },
      },
    ],
  };

  return (
    <section className=" text-center text-white">
      <div className="max-w-sm sm:max-w-2xl ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto">

        {/* Custom Arrows Atas Slider */}
        <div className="flex justify-end gap-2 mb-6 px-2">
          <button
            onClick={() => sliderRef.current?.slickPrev()}
            className=" bg-black/50 hover:bg-black/70 p-1 md:p-2 rounded-full text-white/50 border border-dark-3"
          >
            <ChevronLeft className="h-4 w-4 md:h-6 md:w-6" />
          </button>
          <button
            onClick={() => sliderRef.current?.slickNext()}
            className=" bg-black/50 hover:bg-black/70 p-1 md:p-2 rounded-full text-white/50 border border-dark-3"
          >
            <ChevronRight className="h-4 w-4 md:h-6 md:w-6" />
          </button>
        </div>

        {/* Slider */}
        <div>
          <Slider ref={sliderRef} {...settings}>
            {testimonials.map((item, idx) => (
              <div key={idx} className="px-2 md:px-4 rounded-xl">
                <div
                  className="relative w-full overflow-hidden group aspect-video cursor-pointer rounded-xl"
                  onClick={() => setSelectedVideoId(item.videoId)}
                >
                  <Image
                    src={item.thumbnail}
                    alt={`Testimonial ${idx + 1}`}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    className="object-cover scale-105 transition-transform duration-300 rounded-xl"
                  />
                  <div className="absolute inset-0 bg-dark-2/80 group-hover:bg-transparent transition-opacity duration-300" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-10 h-10 sm:w-12 sm:h-12 bg-primaryVersion2 rounded-full flex items-center justify-center shadow-lg">
                      <BsPlayFill className="text-black text-xl sm:text-2xl" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </Slider>
        </div>
      </div>

      {/* Modal Video */}
      {selectedVideoId && (
        <div
          onClick={() => setSelectedVideoId(null)}
          className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="relative w-full max-w-[90vw] sm:max-w-2xl md:max-w-3xl aspect-video"
          >
            <iframe
              className="w-full h-full rounded-lg"
              src={`https://www.youtube.com/embed/${selectedVideoId}?autoplay=1`}
              title="YouTube video player"
              allow="autoplay; encrypted-media"
              allowFullScreen
            ></iframe>
            <button
              className="absolute -top-6 -right-6 md:-top-8 md:-right-8 lg:-top-12 lg:-right-12 text-white hover:text-red-500 border-2 border-white hover:border-red-500 rounded-full p-1"
              onClick={() => setSelectedVideoId(null)}
            >
              <X size={28} />
            </button>
          </div>
        </div>
      )}
    </section>
  );
};

export default TestimonialSection;
