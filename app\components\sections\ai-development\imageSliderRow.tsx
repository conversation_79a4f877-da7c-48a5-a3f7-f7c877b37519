"use client";
import React from "react";
import Slider from "react-slick";
import Image from "next/image";

interface ImageSliderRowProps {
  images: string[];
  reversed?: boolean;
}

const sliderSettings = {
  dots: false,
  infinite: true,
  speed: 1200,
  slidesToShow: 3,
  slidesToScroll: 1,
  autoplay: true,
  autoplaySpeed: 1200,
  arrows: false,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 3,
        slidesToScroll: 1,
      },
    },
  ],
};

const ImageSliderRow: React.FC<ImageSliderRowProps> = ({ images, reversed }) => {
  return (
    <Slider {...sliderSettings} rtl={reversed} className="w-full">
      {images.map((src, index) => (
        <div key={index} className={`px-2 ${reversed ? "transform scale-x-[-1]" : ""}`}>
          <Image
            src={src}
            alt={`Slider image ${index}`}
            width={250}
            height={250}
            className="object-cover w-full rounded-2xl h-auto"
          />
        </div>
      ))}
    </Slider>
  );
};

export default ImageSliderRow;
