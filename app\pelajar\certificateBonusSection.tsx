
import React from "react";
import CardSertifikat from "../components/cards/cardSertifikat";

const bonuses = [
  {
    title: "Sertifikat Keahlian setelah",
    subtitle: " menyelesaikan kursus",
    image: "/images/cards/section-1/th1.webp",
  },
  {
    title: "Akses komunitas GAMPAR AI",
    subtitle: "(khusus pelajar)",
    image: "/images/cards/section-1/th2.webp",
  },
  {
    title: "<PERSON>eri tambahan",
    subtitle: "(PDF, video rekaman)",
    image: "/images/cards/section-1/th3.webp",
  },
];

const CertificateBonusSection: React.FC = () => {
  return (
    <section className="w-full bg-black text-white py-20 px-6">
      <div className="max-w-7xl mx-auto text-center">
        <h2 className="text-2xl md:text-3xl font-semibold mb-12">
          Sertifikat & Bonus
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 w-full">
          {bonuses.map((bonus, idx) => (
            <CardSertifikat key={idx} image={bonus.image} title={bonus.title} subtitle={bonus.subtitle} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default CertificateBonusSection;