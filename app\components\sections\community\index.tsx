"use client";
import React, { useState } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight, X } from "lucide-react";

const imageRows = [
  ["/images/community/testimonial-1.jpeg", "/images/community/testimonial-2.jpeg", "/images/community/testimonial-3.jpeg", "/images/community/testimonial-4.jpeg", "/images/community/testimonial-5.jpeg"],
  ["/images/community/testimonial-6.jpeg", "/images/community/testimonial-7.jpeg", "/images/community/testimonial-8.jpeg", "/images/community/testimonial-9.jpeg", "/images/community/testimonial-10.jpeg"],
  ["/images/community/testimonial-11.jpeg", "/images/community/testimonial-12.jpeg", "/images/community/testimonial-13.jpeg", "/images/community/testimonial-14.jpeg", "/images/community/testimonial-15.jpeg"],
];

const TestimonialSection: React.FC = () => {
  const [selectedImageIndex, setSelectedImageIndex] = useState<number | null>(null);
  const allImages = imageRows.flat();

  const handlePrev = (e: React.MouseEvent) => {
    e.stopPropagation(); // biar modal ga nutup
    setSelectedImageIndex((prev) => (prev! > 0 ? prev! - 1 : allImages.length - 1));
  };

  const handleNext = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedImageIndex((prev) => (prev! < allImages.length - 1 ? prev! + 1 : 0));
  };

  return (
    <section className="w-full pb-16 md:py-28 bg-black mt-28">
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-center gap-12">
          {/* Left Side */}
          <div className="flex-1 w-full md:w-1/2 flex flex-col gap-6 text-white relative p-6">
            <p className="text-3xl md:text-4xl font-bold leading-snug">
              Komunitas kami
            </p>
            <p className="text-white/50 text-lg font-light leading-relaxed">
              Keseruan kami tiap hari, ya seputar saling sharing dan support tentang karya AI, serta saling membantu buat yang sedang kesulitan.
            </p>
          </div>

          {/* Right Side - Grid Gambar */}
          <div className="flex-1 w-full md:w-1/2 grid grid-cols-3 md:grid-cols-5 gap-1 px-6">
            {allImages.map((src, index) => (
              <div
                key={index}
                className="w-full aspect-[3/7] relative overflow-hidden cursor-pointer group"
                onClick={() => setSelectedImageIndex(index)}
              >
                <Image
                  src={src}
                  alt={`Testimonial ${index + 1}`}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  className="object-cover transition-transform duration-300 ease-in-out group-hover:scale-110"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Modal Section */}
      {selectedImageIndex !== null && (
        <div
          onClick={() => setSelectedImageIndex(null)}
          className="fixed inset-0 bg-black bg-opacity-90 flex justify-center items-center z-50"
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="relative h-[80vh] max-h-[90vh] aspect-[3/4] animate-fade-in scale-95 animate-duration-300"
          >
            <Image
              src={allImages[selectedImageIndex]}
              alt={`Selected Testimonial ${selectedImageIndex + 1}`}
              fill
              className="object-contain rounded-lg shadow-lg"
            />
            <button
              onClick={() => setSelectedImageIndex(null)}
              className="absolute top-4 md:top-[-4rem] right-14 md:right-[-4rem] text-white p-2 hover:bg-white/20 rounded-full transition"
            >
              <X size={36} />
            </button>
            <button
              onClick={handlePrev}
              className="absolute left-14 md:left-[-4rem] top-1/2 -translate-y-1/2 text-white p-2 hover:bg-white/20 rounded-full transition"
            >
              <ChevronLeft size={48} />
            </button>
            <button
              onClick={handleNext}
              className="absolute right-14 md:right-[-4rem] top-1/2 -translate-y-1/2 text-white p-2 hover:bg-white/20 rounded-full transition"
            >
              <ChevronRight size={48} />
            </button>
          </div>
        </div>
      )}
    </section>
  );
};

export default TestimonialSection;
