"use client";

import React, { useEffect, useState } from "react";
import Slider from "react-slick";
import Image from "next/image";
import TiltedCard from "../../TiltedCard";

const DescriptionGamparAI: React.FC = () => {

  const cards = [
    {
      imageSrc: "/images/accessories/home-card-1.webp",
      text: (
        <>
          <span className="text-primaryVersion2 font-bold">Penggunaan AI</span> bukan hanya untuk ahli teknologi saja, bukan hanya untuk golongan tertentu saja, AI adalah sebuah perubahan dalam teknologi itu sendiri, yuk mulai belajar AI dari sekarang dan jadi bagian dari perkembangan teknologi yang luarbiasa ini.
        </>
      ),
    },
    {
      imageSrc: "/images/accessories/home-card-2.webp",
      text: (
        <>
          <span className="text-primaryVersion2 font-bold">GAMPAR AI</span> adalah program belajar menggambar menggunakan teknologi AI (Artificial Intelligence), dengan metode yang paling sederhana dan paling mudah difahami, bahkan oleh Anak-anak dan lansia sekalipun.
        </>
      ),
    },
  ];

  return (
    <section className="w-full ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto -mt-8 md:py-28 px-6 md:px-0">
        <div className="flex flex-col ipad-mini:flex-col md:flex-row w-full gap-0 md:gap-16">
          {/* LEFT */}
          <div className="w-full ipad-mini:w-full md:w-1/2 flex flex-col items-center justify-center gap-8 md:gap-16 md:pb-8 px-4">
            <div className="w-full md:w-[90%] aspect-video rounded-xl">
              <TiltedCard
                imageSrc={cards[0].imageSrc}
                altText="Kenapa MengGAMPAR AI"
                containerHeight="100%"
                containerWidth="100%"
                imageHeight="100%"
                imageWidth="100%"
                rotateAmplitude={12}
                showMobileWarning={false}
                showTooltip={false}
                displayOverlayContent={false}
              />
            </div>
            <p className="text-white/50 text-sm md:text-xl font-light leading-relaxed">{cards[0].text}</p>
          </div>

          {/* RIGHT */}
          <div className="w-full ipad-mini:w-full md:w-1/2 flex flex-col-reverse ipad-mini:flex-col-reverse md:flex-col justify-center items-center gap-8 md:gap-16 pt-8 px-4">
            <p className="text-white/50 text-sm md:text-xl font-light leading-relaxed ipad-mini:text-start md:text-end tracking-wide">
              {cards[1].text}
            </p>
            <div className="w-full md:w-[90%] aspect-video rounded-xl">
              <TiltedCard
                imageSrc={cards[1].imageSrc}
                altText="Kenapa MengGAMPAR AI"
                containerHeight="100%"
                containerWidth="100%"
                imageHeight="100%"
                imageWidth="100%"
                rotateAmplitude={12}
                scaleOnHover={1.1}
                showMobileWarning={false}
                showTooltip={false}
                displayOverlayContent={false}
              />
            </div>
          </div>
        </div>
    </section>
  );
};

export default DescriptionGamparAI;
