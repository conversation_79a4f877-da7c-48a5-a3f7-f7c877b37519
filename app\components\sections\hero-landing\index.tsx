"use client";
import { useEffect, useState } from "react";
import HeroBack<PERSON> from "./HeroBackground";
import Hero<PERSON>ontent from "../../HeroContent";

const HeroSection: React.FC = () => {
  const [isIPad, setIsIPad] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTouchDevice, setIsTouchDevice] = useState(false);
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsIPad(width >= 768 && width < 1024);
      setIsMobile(width < 768);
    };

    const isTouch = "ontouchstart" in window || navigator.maxTouchPoints > 0;
    setIsTouchDevice(isTouch);
    setActiveIndex(0);
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const originalGalleryImages = [
    { src: "/images/cards/section-1/th1.webp", alt: "Boy" },
    { src: "/images/cards/section-1/th2.webp", alt: "Explorer" },
    { src: "/images/cards/section-1/th3.webp", alt: "Creature" },
    { src: "/images/cards/section-1/th4.webp", alt: "Cartoon Animal" },
    { src: "/images/cards/section-1/th5.webp", alt: "Character Design" },
    { src: "/images/cards/section-1/th6.webp", alt: "Anime Style" },
  ];

  const galleryImages = isIPad
    ? originalGalleryImages.slice(0, Math.ceil(originalGalleryImages.length / 1.5))
    : originalGalleryImages;

  return (
    <section className="w-full bg-black text-white">
      <div className="relative h-[50vh] lg:h-[90vh] ">
        <HeroBackground isMobile={isMobile} />
      </div>
      <div className="flex flex-col items-center justify-end px-6 pb-20 text-center">
        <div className="z-10 -mt-36 md:-mt-40 mb-10 md:mb-20 ">
          <h1 className="text-5xl md:text-7xl font-bold text-primaryVersion2 drop-shadow-md">GAMPAR AI</h1>
          <p className="mt-4 text-lg md:text-2xl font-light text-white/70">Cara paling gampang menggambar</p>
          <p className="text-lg md:text-2xl font-light text-white/70">menggunakan AI, bahkan tanpa perlu menulis prompt</p>
        </div>
        <HeroContent
          isMobile={isMobile}
          isIPad={isIPad}
          isTouchDevice={isTouchDevice}
          galleryImages={galleryImages}
          activeIndex={activeIndex}
          setActiveIndex={setActiveIndex}
        />
      </div>
    </section>
  );
};

export default HeroSection;
