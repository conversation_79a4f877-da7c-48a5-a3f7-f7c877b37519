import type { <PERSON><PERSON><PERSON> } from "next";
import { Nunito_Sans } from "next/font/google";
import './globals.css';
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import ClientLayout from "./client-layout";

const nunito = Nunito_Sans({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Landing Page - Gamparai",
  description: "Belajar AI dengan mudah dan hasil yang keren",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${nunito.className} bg-black antialiased`}
      >                  
      <ClientLayout>
        {children}
      </ClientLayout>

      </body>
    </html>
  );
}
