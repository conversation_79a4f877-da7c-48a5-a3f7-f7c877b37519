/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "placehold.co",
        port: "",
        pathname: "/**",
      },
      {
        protocol: "https",
        hostname: "lh3.googleusercontent.com", 
        port: "",
        pathname: "/**",
      },
    ],
  },
  webpack(config) {
    config.module.rules.push({
      test: /\.svg$/,
      use: ["@svgr/webpack"],
    });
    // Enable source maps for production
    if (process.env.NODE_ENV === "production") {
      config.devtool = "source-map";
    }

    return config;
  },
  productionBrowserSourceMaps: false, // Enables source maps in production builds
};

export default nextConfig;
