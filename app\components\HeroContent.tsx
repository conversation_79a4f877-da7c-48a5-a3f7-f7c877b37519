"use client";
import Image from "next/image";
import Slider from "react-slick";

interface GalleryImage {
  src: string;
  alt: string;
}

interface HeroContentProps {
  isMobile: boolean;
  isIPad: boolean;
  isTouchDevice: boolean;
  galleryImages: GalleryImage[];
  activeIndex: number | null;
  setActiveIndex: (index: number | null) => void;
}

const sliderSettings = {
  infinite: true,
  speed: 500,
  slidesToShow: 2,
  slidesToScroll: 2,
  autoplay: true,
  autoplaySpeed: 1000,
  arrows: false,
  responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
      },
    },
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 2,
        slidesToScroll: 2,
      },
    },
  ],
};

const HeroContent: React.FC<HeroContentProps> = ({
  isMobile,
  isIPad,
  isTouchDevice,
  galleryImages,
  activeIndex,
  setActiveIndex,
}) => {
  return (
    <div className="w-full">
      {/* Mobile Version */}
      {(isMobile || isIPad) && (
        <div className="flex items-center justify-center ">
          <div className="w-full max-w-sm">
            <Slider {...sliderSettings}>
              {galleryImages.map((image, index) => (
                <div key={index} className="px-2 rounded-xl cursor-pointer">
                  <Image src={image.src} alt={image.alt} width={400} height={300} className="mx-auto rounded-xl" />
                </div>
              ))}
            </Slider>
          </div>
        </div>
      )}

      {/* iPad/Desktop Version */}
      {!(isMobile || isIPad) && (
        <div className="flex flex-col items-center justify-end text-center">
          <div className="flex justify-center items-center gap-4 overflow-x-auto ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto">
            {galleryImages.map((img, idx) => {
              const isActive = isTouchDevice && activeIndex === idx;

              return (
                <div
                  key={idx}
                  className={`group relative cursor-pointer rounded-xl overflow-hidden transition-all duration-1000 ease-in-out h-[500px] ${
                    isTouchDevice
                      ? isActive
                        ? "w-[400px]"
                        : "w-[200px]"
                      : "w-[200px] hover:w-[400px]"
                  }`}
                  onClick={() => {
                    if (isTouchDevice) setActiveIndex(idx === activeIndex ? null : idx);
                  }}
                >
                  <div
                    className={`absolute inset-0 bg-primaryVersion2/50 z-10 pointer-events-none transition-opacity duration-1000 ${
                      isTouchDevice ? (isActive ? "opacity-0" : "opacity-100") : "group-hover:opacity-0"
                    }`}
                  />
                  <Image
                    src={img.src}
                    alt={img.alt}
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
                    className={`transition-all rounded-xl duration-1000 ease-in-out object-cover object-top origin-top ${isTouchDevice ? (isActive ? "scale-100" : "scale-[1.2]") : "group-hover:scale-100 scale-[1.2]"}`}
                  />
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default HeroContent;
