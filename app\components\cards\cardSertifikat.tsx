
import React from "react";
import Image from "next/image";

interface CardProps {
  image: string;
  title: string;
  subtitle: string;
  className?: string;
}

const CardSertifikat: React.FC<CardProps> = ({ image, title, subtitle, className }) => {
  return (
    <div
      className={`flex flex-col items-center justify-between rounded-2xl border border-dark-3 p-4 pb-6 shadow-md hover:shadow-lg transition duration-300 ${className}`}
    >
      <div className="relative w-full aspect-[4/3] overflow-hidden rounded-xl mb-4">
        <Image
          src={image}
          alt={title}
          fill
          className="object-cover object-top rounded-xl"
        />
      </div>
      <p className="text-base md:text-xl text-white/80 text-center px-2">
        {title}
      </p>
      <p className="text-base md:text-xl text-white/80 text-center px-2">
        {subtitle}
      </p>
      
    </div>
  );
};

export default CardSertifikat;
