import React from "react";
import ReasonSection from "../components/sections/reason-landing";
import ProofSection from "../components/sections/proof";
import AIDevelopmentSection from "../components/sections/ai-development";
import ModulesSection from "../components/sections/modules";
import PricingSection from "../components/sections/pricing";
import CtaSection from "../components/sections/CTA";
import FAQSection from "../components/sections/FAQSection";
import TestimonialSection from "../components/shared/Testimonial";
import HeroSection from "../components/sections/hero-landing";
import FeaturesSection from "../components/sections/features";
import { Metadata } from "next";
import YoutubeShortsSection from "../components/sections/youtubeShorts";
import CommunitySection from "../components/sections/community";
import { faqsUmum } from "../datas/faq";

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3030"),
  title: "Aigensee - Garisprtama - Gampar AI",
  description: "Aigensee - Kursus Online AI and Animation.",
  openGraph: {
    title: "Aigensee - Kursus Online AI and Animation",
    description: "Aigensee - Kursus Online AI and Animation.",
    url: "/",
    images: ["/images/accessories/Aigensee-Logo.png"],
  },
};
const LandingPage: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center mt-10 lg:mx-auto relative overflow-x-hidden">
      <HeroSection />
      <ReasonSection />
      <ProofSection/>
      <YoutubeShortsSection/>
      <FeaturesSection />
      <AIDevelopmentSection/>
      <ModulesSection/>
      <PricingSection/>
      <h2 className="text-2xl md:text-4xl font-bold mb-2 text-white mt-20">Testimoni</h2>
        <p className="text-sm md:text-base my-6 md:my-10 text-white/50">
          Ini kata para member kami.
        </p>
      <TestimonialSection/>
      {/* <div className="w-full py-4 md:py-32">
        <div className="ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto text-white flex text-center text-xl md:text-2xl  px-6 ">
          <p className="hidden md:block text-white/80">
            <span className="inline-flex"><RiDoubleQuotesL className="text-primary text-sm"/>Yuk gabung dengan Member lainnya, ingat kita udah berada di industri 5.0 yang artinya kolaborasi</span>
            <span className="inline-flex">manusia dengan AI, klo lu gak menguasai AI dari sekarang. siap siap ketinggalan<RiDoubleQuotesR className="text-primary text-sm" /></span>
          </p>

           <p className="block md:hidden font-nunito text-white/80 text-xs">
            <span className="inline-flex"><RiDoubleQuotesL className="text-primary text-xs md:text-sm"/>Yuk gabung dengan Member lainnya, ingat kita</span>
            <span> udah berada di industri 5.0 yang artinya kolaborasi</span>
            <span>manusia dengan AI, klo lu gak menguasai AI dari</span>
            <span className="inline-flex">sekarang. siap siap ketinggalan<RiDoubleQuotesR className="text-primary text-xs md:text-sm" /></span>
          </p>
        </div>
      </div> */}
      <CommunitySection/>
      <div className="ipad-mini:max-w-xl ipad:max-w-2xl lg:max-w-3xl mx-auto text-white">
        <h2 className="text-2xl md:text-3xl font-bold md:text-center mb-8">
          Paling Sering Ditanyakan
        </h2>
        <p className="text-xs md:text-lg md:text-center md:mx-24 mb-8 text-white/50">Berikut beberapa pertanyaan yang sering diajukan oleh member kami. Mungkin kamu juga punya pertanyaan yang sama?</p>
      </div>

      <FAQSection faqs={faqsUmum} />
      <CtaSection/>
      
      
    </div>
  );
};

export default LandingPage;