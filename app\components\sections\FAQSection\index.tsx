"use client"
import { useState } from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";


interface FAQ {
  title: string;
  item: string;
}

interface FAQSectionProps {
  faqs: FAQ[];
}

const FAQSection: React.FC<FAQSectionProps> = ({ faqs }) => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const toggleFAQ = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="w-full px-6 bg-black text-white">
      <div className="ipad-mini:max-w-xl ipad:max-w-2xl lg:max-w-3xl mx-auto">
        <div className="space-y-4">
          {faqs.map((faq, index) => {
            const isOpen = openIndex === index;

            return (
              <div key={index} className="bg-dark-2 rounded-md overflow-hidden transition-all duration-300">
                <button
                  onClick={() => toggleFAQ(index)}
                  className={`w-full text-left p-4 flex justify-between items-center font-semibold text-xs md:text-md lg:text-xl transition-colors duration-300 ${
                    isOpen ? "bg-primaryVersion2 text-black" : "bg-dark-2 text-white"
                  }`}
                >
                  {faq.title}
                  {isOpen ? <FaChevronUp className="ml-4" /> : <FaChevronDown className="ml-4"  />}
                </button>

                <div
                  className={`transition-all duration-500 ease-in-out overflow-hidden ${
                    isOpen ? "max-h-screen opacity-100 py-4 px-6" : "max-h-0 opacity-0 py-0 px-6"
                  }`}
                >
                  {faq.item.split('\n').map((line, i) => (
                    <span key={i} className="text-xs md:text-xl font-light">
                      {line}
                      <br />
                    </span>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default FAQSection;