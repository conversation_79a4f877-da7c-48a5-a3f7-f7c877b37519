import React from "react";

const ProfileMentorSection: React.FC = () => (
  <section className="w-full text-white py-20 px-6">
    <div className="max-w-7xl mx-auto text-center">
      <h2 className="text-2xl md:text-3xl font-bold mb-12">
        Profile Mentor
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 w-full justify-center md:max-w-2xl mx-auto">
        <div className="bg-dark-2 rounded-2xl p-6 text-left mx-8 md:mx-auto">
          <img
            src="/images/mentors/yudha.webp"
            alt="Yudha Pangesti"
            className="w-full aspect-square object-top object-cover rounded-lg"
          />
          <h3 className="text-xl font-bold my-4"><PERSON><PERSON></h3>
          <p className="text-lg font-light text-white/50 tracking-wide">
            Praktisi AI Art dan <PERSON>or Visual Digital
          </p>
        </div>
        <div className="bg-dark-2 rounded-2xl p-6 text-left mx-8 md:mx-auto">
          <img
            src="/images/mentors/wanda.webp"
            alt="Yudha Pangesti"
            className="w-full aspect-square object-top object-cover rounded-lg"
          />
          <h3 className="text-xl font-bold my-4">Wanda Hana</h3>
          <p className="text-lg font-light text-white/50 tracking-wide">
            Fasilitator kreatif teknologi & mentor pembelajaran AI untuk pelajar
          </p>
        </div>
      </div>      
    </div>
  </section>
);

export default ProfileMentorSection;