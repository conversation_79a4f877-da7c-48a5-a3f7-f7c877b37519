"use client";
import { useEffect, useState } from "react";
import Hero<PERSON>ontent from "../../HeroContent";
import HeroBackground from "./HeroBackground";

const HeroPelajarSection: React.FC = () => {
  const [isIPad, setIsIPad] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTouchDevice, setIsTouchDevice] = useState(false);
  const [activeIndex, setActiveIndex] = useState<number | null>(null);

  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsIPad(width >= 768 && width < 1024);
      setIsMobile(width < 768);
    };

    const isTouch = "ontouchstart" in window || navigator.maxTouchPoints > 0;
    setIsTouchDevice(isTouch);
    setActiveIndex(0);
    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const originalGalleryImages = [
    { src: "/images/cards/section-1/th1.webp", alt: "Boy" },
    { src: "/images/cards/section-1/th2.webp", alt: "Explorer" },
    { src: "/images/cards/section-1/th3.webp", alt: "Creature" },
    { src: "/images/cards/section-1/th4.webp", alt: "Cartoon Animal" },
    { src: "/images/cards/section-1/th5.webp", alt: "Character Design" },
    { src: "/images/cards/section-1/th6.webp", alt: "Anime Style" },
  ];

  const galleryImages = isIPad
    ? originalGalleryImages.slice(0, Math.ceil(originalGalleryImages.length / 1.5))
    : originalGalleryImages;

  return (
    <section className="w-full bg-black text-white">
      <div className="relative h-[50vh] lg:h-screen ">
        <HeroBackground isMobile={isMobile} />
        <div className="relative  h-[50vh] lg:h-screen ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto z-10">
          <div className="absolute top-0 left-0 h-full w-full flex items-end md:items-center justify-center md:justify-start">
            <h1 className="text-xl  ipad-mini:text-4xl md:text-4xl lg:text-7xl font-bold text-white drop-shadow-md md:w-1/2 text-center md:text-left ">Kursus Pembelajaran AI untuk Pelajar Pertama di Indonesia</h1>
          </div>
        </div>
      </div>
      <div className="flex flex-col items-center justify-end px-6 pt-10 pb-20 text-center">
        <HeroContent
          isMobile={isMobile}
          isIPad={isIPad}
          isTouchDevice={isTouchDevice}
          galleryImages={galleryImages}
          activeIndex={activeIndex}
          setActiveIndex={setActiveIndex}
        />
      </div>
    </section>
  );
};

export default HeroPelajarSection;
