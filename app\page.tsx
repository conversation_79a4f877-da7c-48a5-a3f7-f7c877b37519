import HeroHomeSection from "./components/sections/hero-home";
import BenefitGamparAI from "./components/sections/hero-home/benefit-gampai-ai";
import ClassSelection from "./components/sections/hero-home/class-selection";
import DescriptionGamparAI from "./components/sections/hero-home/description-gampar-ai";
import MapsGamparAI from "./components/sections/hero-home/maps";
import TestimonialSection from "./components/shared/Testimonial";

export default function Home() {
  return (
      <div className="flex flex-col items-center justify-center mt-10 lg:mx-auto relative overflow-x-hidden">
        <HeroHomeSection />
        <DescriptionGamparAI/>
        <BenefitGamparAI/>
        <ClassSelection/>
        <h2 className="text-2xl md:text-4xl font-bold text-white"><PERSON><PERSON><PERSON> yang <PERSON>t</h2>
        <div className="py-8 md:py-12">
        <TestimonialSection/>
        </div>
        <MapsGamparAI/>

        
      </div>
  );
}

