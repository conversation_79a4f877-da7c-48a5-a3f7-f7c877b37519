"use client";
import Link from "next/link";
import { FaWhatsapp } from "react-icons/fa";
import MapsGamparAI from "../components/sections/hero-home/maps";
import MapIframe from "../components/shared/MapIframe";

const ContactPage = () => {
  const redirectToMaps = () => {
    window.open("https://maps.app.goo.gl/1bvXevbz7KaUBMEm9", "_blank");
  };
  return (
    <div className="ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl md:mx-auto py-28 md:py-0 mt-12 md:mt-0 md:min-h-screen flex flex-col items-center justify-center bg-black text-white px-4">
      {/* Headline */}
      <div className="text-center mb-6">
        <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold leading-snug">
          Yuk ngobrol <br />
          <span className="text-white">bareng tim <PERSON>!</span>
        </h1>
        <p className="mt-4 text-sm sm:text-base text-white/50 px-6">
          Kami siap bantu kamu lebih dekat dengan teknologi AI.
        </p>
      </div>

      {/* WhatsApp Button */}
      <Link
        href="https://wa.me/6289519442003?text=Halo%20admin%2C%20saya%20mau%20tanya%20tentang%20program%20ini" // Ganti dengan nomor WhatsApp kamu
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-medium transition duration-300"
      >
        <FaWhatsapp className="text-lg" />
        Lanjut Whatsapp
      </Link>

      {/* Address Box */}
      <div className="mt-10 border border-dark-3 rounded-md p-4 w-full max-w-3xl bg-black/10 backdrop-blur-sm">
        <h3 className="text-base md:text-xl text-white font-semibold ">Alamat Kami</h3>
        <p className="text-white/50 text-sm leading-relaxed mb-6">
          Perum Hegarmanah Asri No.8 Blok A8, Hegarmanah, Kec. Cikancung,
          Kabupaten Bandung, Jawa Barat 40396
        </p>
        {/* Iframe Container with Absolute Button */}
        <div className="relative w-full aspect-video ">
          {/* Button absolute */}
          <button
            onClick={redirectToMaps}
            className="hidden md:flex absolute top-4 right-4 z-20 bg-primaryVersion2 hover:bg-secondary text-black font-semibold px-4 py-2 rounded-md transition"
          >
            Buka di Google Maps
          </button>
          <MapIframe />
        </div>

        <button
            onClick={redirectToMaps}
            className="flex justify-center md:hidden bg-primaryVersion2 hover:bg-secondary text-black font-semibold px-4 py-2 rounded-md transition mt-6 mx-auto"
          >
          Buka di Google Maps
        </button> 
      
      </div>

    </div>
  );
};

export default ContactPage;
