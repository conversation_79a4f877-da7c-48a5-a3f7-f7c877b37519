import React from "react";
import { FaCircleCheck } from "react-icons/fa6";

const skills = [
  "Mengenal AI dari luar dan dalam",
  "Mengenal batasan dan aturan penggunaan AI",
  'Mengenal cara kerja "prompt" (bahasa komunikasi dengan AI)',
  "Menggunakan AI untuk menggambar & desain kreatif",
  "Membuat video dan konten digital dengan bantuan AI",
  "Membentuk mindset positif & etis dalam bersosial media",
  "Melatih cara berpikir kreatif dan bijak dalam dunia digital",
];

const LearnedSkillsSection: React.FC = () => {
  return (
    <section className="w-full py-20 px-6 text-white">
      <div className="max-w-3xl mx-auto text-center">
        <h2 className="text-2xl md:text-3xl font-semibold mb-12">
          <PERSON>-Put<PERSON>
        </h2>

        <div className="flex flex-col gap-4">
          {skills.map((item, index) => (
            <div
              key={index}
              className="flex items-center bg-dark-2 rounded-xl px-6 py-4 text-left text-sm md:text-base text-white"
            >
              <FaCircleCheck className="text-primaryVersion2 mr-4 w-4 h-4 md:w-5 md:h-5" />

              {item}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default LearnedSkillsSection;
