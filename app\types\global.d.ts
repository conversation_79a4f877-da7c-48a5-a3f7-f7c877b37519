// global.d.ts

declare module '*.glb';
declare module '*.png';

declare module 'meshline' {
  export const MeshLineGeometry: any;
  export const MeshLineMaterial: any;
}
declare global {
  interface Window {
    mychat: {
      server: string;
      iframeWidth: string;
      iframeHeight: string;
      accessKey: string;
    };
    fbq: any;
    instgrm?: {
      Embeds: {
        process(): void;
      };
    };

  }
  namespace JSX {
    interface IntrinsicElements {
      meshLineGeometry: any;
      meshLineMaterial: any;
    }
  }
}

export {};

