"use client";
import React, { useState } from "react";
import Image from "next/image";
import clsx from "clsx";

const ProofImage: React.FC = () => {
  const images = [
    "/images/accessories/proof-1.webp",
    "/images/accessories/proof-2.webp",
    "/images/accessories/proof-3.webp",
    "/images/accessories/proof-4.webp",
    "/images/accessories/proof-5.webp",
    "/images/accessories/proof-6.webp",
  ];
  const [nextIndex, setNextIndex] = useState<number | null>(null);

  const [activeIndex, setActiveIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isAnimatingImage2, setIsAnimatingImage2] = useState(false);
  const [layerRotations, setLayerRotations] = useState<number[]>(
    images.slice(1).map((_, i) => (i + 1) * -4) 
  );
  const handleClick = () => {
    if (isAnimating) return;

    const upcomingIndex = (activeIndex + 1) % images.length;
    setNextIndex(upcomingIndex); 

    setIsAnimatingImage2(true);
    setIsAnimating(true);

    setLayerRotations((prev) =>
      prev.map((angle) => angle + 4 <= 12 ? angle + 4 : angle - 28)
    );

    setTimeout(() => {
      setActiveIndex(upcomingIndex);
      setNextIndex(null); 
      setIsAnimating(false);
    }, 1200);

    setTimeout(() => {
      setIsAnimatingImage2(false);
    }, 900);
  };

  const getImageIndex = (offset: number) => {
    const index = (activeIndex + offset) % images.length;
    return index >= 0 ? index : images.length + index;
  };

  return (
    <div className="flex-1 w-full relative flex items-center justify-center">
      {images.slice(1).map((_, i) => {
        const imageIndex = getImageIndex(images.length - 1 - i);
        const targetIndex = nextIndex ?? activeIndex; 

        const isTopLayer = imageIndex === targetIndex;

        return (
          <div
            key={i}
            className={clsx(
              "absolute w-[300px] ipad-mini:w-[500px] lg:w-[395px] aspect-square rounded-2xl transition-all overflow-hidde border-[30px] border-primaryVersion2 transition-opacity duration-500",
              {
                "opacity-100": isTopLayer,
                "opacity-50": !isTopLayer,
              }
            )}
            style={{
              zIndex: isTopLayer ? images.length + 1 : i,
              transform: `rotate(${isTopLayer ? 0 : layerRotations[images.length - 1 - i]}deg)`,
              transition: 'transform 0.5s ease',
              transitionDelay: `${i * 0.1}s`,
            }}
          >
            <Image
              src={images[imageIndex]}
              alt={`Layer ${i + 1}`}
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
              className="object-cover bg-primaryVersion2 w-full h-full"
            />
          </div>
        );
      })}

      {/* Active Image */}
      <div
        className={clsx(
          "relative z-20 w-[300px] ipad-mini:w-[500px] lg:w-[395px] aspect-square rounded-2xl overflow-hidde border-[30px] border-primaryVersion2 cursor-pointer",
          {
            "animate-dropFade": isAnimating,
          }
        )}
        onClick={handleClick}
      >
        <Image
          src={images[getImageIndex(0)]}
          alt="Active Layer"
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
          className="object-cover bg-primaryVersion2 w-full h-full"
        />
      </div>

      {/* Animated Next Image Below */}
      <div
        className={clsx(
          "absolute -z-50 w-[300px] ipad-mini:w-[500px] lg:w-[395px] aspect-square top-[160%] rounded-2xl overflow-hidden  border-[30px] border-primaryVersion2 cursor-pointer opacity-0",
          {
            "opacity-100": isAnimatingImage2,
            "animate-riseFade": !isAnimatingImage2,
          }
        )}
      >
        <Image
          src={images[getImageIndex(-1)]}
          alt="Previous Layer"
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 400px"
          className="object-cover bg-primaryVersion2 w-full h-full"
        />
      </div>

    </div>
  );
};

export default ProofImage;
