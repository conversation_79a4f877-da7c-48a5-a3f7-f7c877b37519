"use client";
import React from "react";

const PricingSection: React.FC = () => {
  return (
    <section className="w-full bg-black text-white py-20 px-6">
      <div className="max-w-7xl mx-auto text-center">
        {/* Title & Subtitle */}
        <h2 className="text-2xl md:text-3xl font-bold mb-12">
          Biaya Kursus
        </h2>
        <p className="text-sm md:text-lg font-light text-white/50 mb-6">
          Promo Bulan Juli
        </p>

        {/* Table */}
        <div className="overflow-x-auto md:mx-20">
          <table className="min-w-full border-collapse text-sm md:text-xl">
            <thead>
              <tr className="bg-yellow-500 text-black font-semibold">
                <th className="w-1/2 py-3 px-4 rounded-tl-xl"><PERSON><PERSON><PERSON></th>
                <th className="w-1/2 py-3 px-4 rounded-tr-xl">Total Biaya</th>
              </tr>
            </thead>
            <tbody className="bg-dark-2 text-white">
              <tr className="border-t border-dark-3">
                <td className="py-3 px-4 border-r border-dark-3">1 Orang</td>
                <td className="py-3 px-4">Rp250.000</td>
              </tr>
              <tr className="border-t border-dark-3">
                <td className="py-3 px-4 border-r border-dark-3">2 Orang*</td>
                <td className="py-3 px-4">Rp350.000</td>
              </tr>
              <tr className="border-t border-dark-3">
                <td className="py-3 px-4 border-r border-dark-3">3 Orang*</td>
                <td className="py-3 px-4">Rp450.000</td>
              </tr>
              <tr className="border-t border-dark-3">
                <td className="py-3 px-4 border-r border-dark-3 rounded-bl-xl">4–6 Orang*</td>
                <td className="py-3 px-4 rounded-br-xl">Rp550.000</td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Note */}
        <p className="text-xs md:text-lg mt-8 text-white/50 italic">
          *Bisa patungan dengan teman untuk lebih hemat!
        </p>
      </div>
    </section>
  );
};

export default PricingSection;
