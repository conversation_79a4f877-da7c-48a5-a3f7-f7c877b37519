"use client";
import Link from "next/link";
import { useState } from "react";
import { FaShoppingCart } from "react-icons/fa";
import Image from "next/image";
import ButtonSolid from "./buttons/ButtonSolid";
import ButtonOutline from "./buttons/ButtonOutline";
import { useRouter } from "next/navigation";
import useClickOutside from "../hooks/useClickOutside";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";

const menuItems = [
//   { href: "/tentang-kami", label: "Tentang kami" },
//   { href: "/services", label: "Layanan" },
];

const serviceItems = [
  { href: "/umum", label: "Umum" },
  { href: "/pelajar", label: "Pelajar" },
];



const DesktopNavbar = () => {
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [serviceDropdownOpen, setServiceDropdownOpen] = useState(false);
    
    const toggleDropdown = () => setDropdownOpen((prev) => !prev);
    const closeDropdown = () => setDropdownOpen(false);
    const toggleServiceDropdown = () => setServiceDropdownOpen((prev) => !prev);
    const dropdownRef = useClickOutside(() => setDropdownOpen(false));
    const serviceDropdownRef = useClickOutside(() => setServiceDropdownOpen(false));
    const closeServiceDropdown = () => setServiceDropdownOpen(false);
    const loginUrl = process.env.NEXT_PUBLIC_LOGIN_URL || "/login";
    const signupUrl = process.env.NEXT_PUBLIC_SIGNUP_URL || "/sign-up";

    const handleButtonLogin = () => {
    window.location.href = loginUrl;
    };

    const handleButtonSignUp = () => {
    window.location.href = signupUrl;
    };

    return (
        <div className="hidden lg:flex w-full justify-between space-x-8 mx-6 md:mx-0 ">
            {/* Logo */}
            <Link href="/" className="inline-block">
                <div className="relative w-[180px] h-[80px]">
                    <Image
                    src="/images/accessories/Aigensee-Logo-horizontal.png"
                    alt="Logo"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    priority
                    className="object-contain"
                    />
                </div>
            </Link>
            <nav className="flex justify-center items-center space-x-8">
                
                <div className="relative" ref={serviceDropdownRef}>
                    <button
                    className="flex w-full justify-center items-center gap-2 text-white border-b-2 border-transparent hover:border-primaryYellow hover:text-primaryYellow transition"
                    onClick={toggleServiceDropdown}
                    >
                    Kelas {serviceDropdownOpen ? <IoIosArrowUp /> : <IoIosArrowDown />}
                    </button>

                    {serviceDropdownOpen && (
                    <div className="absolute left-0 mt-2 w-48 bg-dark-2 text-white rounded-lg shadow-lg py-2">
                        {serviceItems.map((service) => (
                        <Link
                            key={service.href}
                            href={service.href}
                            className="block px-4 py-2 hover:bg-gray-700 hover:text-primaryYellow transition"
                            onClick={closeServiceDropdown}
                        >
                            {service.label}
                        </Link>
                        ))}
                    </div>
                    )}
                </div>
                {/* {menuItems.map((item) => (
                    <Link
                    key={item.href}
                    href={item.href}
                    className="text-white border-b-2 border-transparent hover:text-primaryVersion2 dark:hover:text-primaryVersion2 hover:border-primaryVersion2 dark:hover:border-primaryVersion2 transition-colors duration-300"
                    >
                    {item.label}
                    </Link>
                ))} */}
                <Link
                    key="kontak"
                    href="/kontak"
                    className="text-white border-b-2 border-transparent hover:text-primaryVersion2 dark:hover:text-primaryVersion2 hover:border-primaryVersion2 dark:hover:border-primaryVersion2 transition-colors duration-300"
                    >
                    Kontak
                </Link>
            </nav>

            {/* Button */}
            <div className="flex items-center space-x-6">
                <div className="flex space-x-4">
                    <ButtonOutline onClick={handleButtonLogin}>Masuk</ButtonOutline>
                    <ButtonSolid onClick={handleButtonSignUp}>Daftar</ButtonSolid>
                </div>
            </div>
        </div>
    );
};

export default DesktopNavbar;
