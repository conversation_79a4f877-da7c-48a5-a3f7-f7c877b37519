
import React from "react";


const InvestmentsSection: React.FC = () => {

  return (
    <section className="z-30 w-full py-20 px-6 relative bg-black">
      <div className="ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto flex flex-col ipad-mini:flex-col md:flex-row gap-10 justify-center">
        <h2 className="w-full text-white text-center text-base lg:text-3xl text-left font-semibold tracking-wide px-6 lg:mx-64 mb-12">
          Investasi terbaik untuk masa depan putra-putri kita bukan lagi sebatas pendidikan formal.
        </h2>
      </div>
      <div className="ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto flex flex-col ipad-mini:flex-col md:flex-row gap-10 justify-center">
        {/* Judul */}

        <h2 className="w-full ipad-mini:w-full md:w-1/2 text-white text-2xl lg:text-3xl text-left ipad-mini:text-center bg-dark-2 p-6 rounded-xl">
        </h2>

        {/* Fitur Grid */}
        <div className="w-full ipad-mini:w-full md:w-1/2 grid grid-cols-1 gap-8">
          
          <div className="flex flex-col border border-dark-3 rounded-xl px-6 md:px-12 py-8 md:py-24 hover:border-primaryVersion2 transition">
            <p className="text-white/50 text-sm md:text-2xl">
              Sekarang saatnya memperkenalkan mereka pada teknologi yang sedang membentuk dunia: <span className="text-primaryVersion2">AI (Artificial Intelligence)</span>.
            </p>
          </div>
          <div className="flex flex-col border border-dark-3 rounded-xl px-6 md:px-12 py-8 md:py-24 hover:border-primaryVersion2 transition">
            <p className="text-white/50 text-sm md:text-2xl">
              Di <span className="text-primaryVersion2">GAMPAR AI</span>, kami membantu putra-putri <span className="text-primaryVersion2">belajar, paham, dan bijak menggunakan AI</span> — bukan hanya untuk hiburan, tapi sebagai <span className="text-primaryVersion2">bekal skill masa depan</span>.
            </p>
          </div>
      
        </div>
      </div>
    </section>
  );
};

export default InvestmentsSection;
