"use client";
import React from "react";
import Image from "next/image";
import { FaPlay } from "react-icons/fa";

interface VideoProps {
  playVideo: boolean;
  setPlayVideo: (val: boolean) => void;
}

const VideoPlayer: React.FC<VideoProps> = ({ playVideo, setPlayVideo }) => {
  return !playVideo ? (
    <div className="relative w-full h-full cursor-pointer" onClick={() => setPlayVideo(true)}>
      <Image
        src="/images/background/landing0.webp"
        alt="Video Placeholder"
        fill
        className="object-cover rounded-2xl"
      />
      <div className="absolute z-20 inset-0 flex items-center justify-center">
        <div className="bg-primaryVersion2 rounded-full p-6">
          <FaPlay />
        </div>
      </div>
      <div className="absolute inset-0 bg-font/50 z-10 pointer-events-none transition-opacity duration-1000" />
    </div>
  ) : (
    <iframe
      width="100%"
      height="100%"
      src="https://www.youtube.com/embed/i5E1miSueSE?autoplay=1"
      title="Welcome to Aigensee"
      frameBorder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
      referrerPolicy="strict-origin-when-cross-origin"
      allowFullScreen
      className="rounded-xl"
    ></iframe>
  );
};

export default VideoPlayer;
