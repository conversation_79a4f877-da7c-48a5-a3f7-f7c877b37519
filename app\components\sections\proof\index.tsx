// components/ProofSection.tsx
import React from "react";
import ProofImage from "./proofImage";
import ProofText from "./proofText";
import SectionHeading from "./SectionHeading";

const ProofSection: React.FC = () => {
  return (
    <section className="w-full z-20 mx-6 py-16 lg:py-36 dark:bg-black">
      <div className="ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto flex flex-col ipad-mini:flex-col md:flex-row items-center">
        <SectionHeading className="block lg:hidden" />
        <ProofText />
        <ProofImage />

      </div>
    </section>
  );
};

export default ProofSection;
