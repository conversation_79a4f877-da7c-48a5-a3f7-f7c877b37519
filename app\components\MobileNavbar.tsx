"use client";
import { useState } from "react";
import Link from "next/link";
import { IoClose } from "react-icons/io5";
import { IoIosArrowUp,IoIosArrowDown  } from "react-icons/io";

import { FaBars, FaShoppingCart } from "react-icons/fa";
import Image from "next/image";
import useClickOutside from "../hooks/useClickOutside";
import ButtonOutline from "./buttons/ButtonOutline";
import ButtonSolid from "./buttons/ButtonSolid";

const menuItems = [
  // { href: "/tentang-kami", label: "Tentang Kami" },
];

const serviceItems = [
  { href: "/umum", label: "Umum" },
  { href: "/pelajar", label: "Pelajar" },
];


const MobileNavbar = () => {
  const [menuOpen, setMenuOpen] = useState(false);
  const [serviceDropdownOpen, setServiceDropdownOpen] = useState(false);
  const menuRef = useClickOutside(() => setMenuOpen(false));

  const loginUrl = process.env.NEXT_PUBLIC_LOGIN_URL || "/login";
  const signupUrl = process.env.NEXT_PUBLIC_SIGNUP_URL || "/sign-up";

  const handleButtonLogin = () => {
    window.location.href = loginUrl;
  };

  const handleButtonSignUp = () => {
    window.location.href = signupUrl;
  };

  return (
    <div className="flex w-full justify-between items-center mx-6 lg:hidden">

   
      {/* Logo */}
      <Link href="/" className="inline-block">
          <div className="relative w-[180px] h-[80px]">
              <Image
              src="/images/accessories/Aigensee-Logo-horizontal.png"
              alt="Logo"
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
              priority
              className="object-contain"
              />
          </div>
      </Link>
 
      <div className="p-2 cursor-pointer" onClick={() => setMenuOpen((prev) => !prev)}>
        {menuOpen ? <IoClose className="text-3xl text-primaryYellow" /> : <FaBars className="text-3xl text-primary" />}
      </div>
      
      

      {/* Mobile Dropdown Menu */}
      {menuOpen && (
        <div ref={menuRef} className="absolute top-20 left-0 w-full bg-font text-center px-6 text-2xl py-4 space-y-2 border-t-2 border-primaryYellow">
          
          {/* Services Dropdown */}
          <div className="relative">
            <button
              className="flex w-full justify-center items-center text-white gap-2"
              onClick={() => setServiceDropdownOpen((prev) => !prev)}
            >
              Kelas {serviceDropdownOpen ? <IoIosArrowUp /> : <IoIosArrowDown />}
            </button>

            {serviceDropdownOpen && (
              <div className="mt-2 bg-dark-2 text-white rounded-lg shadow-lg py-2">
                {serviceItems.map((service) => (
                  <Link
                    key={service.href}
                    href={service.href}
                    className="block px-4 py-2 hover:bg-gray-700 hover:text-primaryYellow transition"
                    onClick={() => {
                      setServiceDropdownOpen(false);
                      setMenuOpen(false);
                    }}
                  >
                    {service.label}
                  </Link>
                ))}
              </div>
            )}
          </div>
          {/* {menuItems.map((item) => (
            <Link key={item.href} href={item.href} className="block text-white" onClick={() => setMenuOpen(false)}>
              {item.label}
            </Link>
          ))} */}
          <Link
              key="kontak"
              href="/kontak"
              className="flex w-full justify-center items-center text-white border-b-2 border-transparent hover:text-primaryVersion2 dark:hover:text-primaryVersion2 hover:border-primaryVersion2 dark:hover:border-primaryVersion2 transition-colors duration-300"
              >
              Kontak
          </Link>

          <div className="flex items-center justify-center space-x-6">
                <div className="flex space-x-4">
                    <ButtonOutline onClick={handleButtonLogin}>Masuk</ButtonOutline>
                    <ButtonSolid onClick={handleButtonSignUp}>Daftar</ButtonSolid>
                </div>
            </div>
        </div>
      )}
    </div>
  );
};

export default MobileNavbar;
