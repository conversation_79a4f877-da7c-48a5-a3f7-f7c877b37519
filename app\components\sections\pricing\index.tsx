'use client';

import Link from 'next/link';
import React from 'react';

const PricingSection = () => {
  const handleTrackClick = (type: 'full' | 'rent') => {
    if (typeof window !== 'undefined' && typeof window.fbq === 'function') {
      const contentName = type === 'full' ? 'Gampar AI Selamanya' : 'Gampar AI Sewa';
      const value = type === 'full' ? 125000 : 85000;
      const contentIds = type === 'full' ? ['SKU-1'] : ['SKU-2'];
      const quantity = 1;

      window.fbq('track', 'InitiateCheckout', {
        content_name: contentName,
        value: value,
        currency: 'IDR',
        contents: [{ id: contentIds[0], quantity }],
        content_type: 'product',
      });
    }
  };

  return (
    <section className="w-full px-6 bg-black py-16 text-white">
      <div className="w-full ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto text-center">
        <h2 className="text-2 lg:text-3xl font-semibold mb-12">
          <PERSON><PERSON><PERSON>
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Card 1 - Akses Selamanya */}
          <div className="bg-dark-2 rounded-2xl p-4 lg:p-6 ">
            <div className="flex w-full gap-4 items-center justify-center">
              <div className="flex w-[245px] items-center justify-center">
                <img
                  src="/images/thumbnail/courses/card.webp"
                  alt="Akses Selamanya"
                  className="w-full h-full object-cover rounded-lg"
                />
              </div>
              <div className="flex w-1/2 flex-col items-start">
                <p className="text-red-800 line-through text-xs ipad-mini:text-base lg:text-base mb-1">Rp325.000</p>
                <h4 className="text-white text-sm ipad-mini:text-xl lg:text-xl font-bold mb-1">Rp139.000</h4>
                <p className="text-xs ipad-mini:text-sm lg:text-sm mb-4 font-light text-white/50">Kelas Online</p>
                <Link
                  href="https://yudhapangesti.orderonline.id/gampar-ai-selamanya"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={() => handleTrackClick('full')}
                  className="w-full bg-primary hover:bg-secondary text-black text-sm lg:text-xl font-semibold py-1 lg:py-2 px-6 rounded-lg transition"
                >
                  Beli Kelas
                </Link>
              </div>
            </div>
          </div>

          {/* Card 2 - Sewa */}
          <div className="bg-dark-2 rounded-2xl p-4 lg:p-6 ">
            <div className="flex w-full gap-4 items-center justify-center">
              <div className="flex w-[245px] items-center justify-center">
                <img
                  src="/images/thumbnail/courses/card.webp"
                  alt="Akses Selamanya"
                  className="w-full h-full object-cover rounded-lg"
                />
              </div>
              <div className="flex w-1/2 flex-col items-start">
                <p className="text-red-800 line-through text-xs ipad-mini:text-base lg:text-base mb-1">Rp550.000</p>
                <p className="text-white text-sm ipad-mini:text-xl lg:text-xl font-bold mb-1">Rp290.000</p>
                <p className="text-xs ipad-mini:text-sm lg:text-sm mb-4 font-light text-white/50">Kelas Offline</p>
                <Link
                  href="https://yudhapangesti.orderonline.id/gampar-ai-sewa"
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={() => handleTrackClick('rent')}
                  className="w-full bg-primary hover:bg-secondary text-black text-sm lg:text-2xl font-bold py-1 lg:py-2 px-6 rounded-lg transition"
                >
                  Atur Jadwal
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
