import React from "react";
import Image from "next/image";

const HeroHomeSection: React.FC = () => {
  const headingText = "Mending kita yang ngeGAMPAR AI";
  const subheadingText = "Daripada kita yang diGAMPAR AI,";

  return (
    <section className="bg-black w-full overflow-hidden">
      {/* Background */}
      <div className="relative aspect-square lg:h-[1200px] w-full hidden md:block">
        <Image
          src="/images/background/landing-home.webp"
          alt="Landing Background"
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
          className="object-cover"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-b from-black via-black/70 to-black" />
      </div>

      {/* Content */}
      <div className="relative z-10 flex flex-col items-center justify-end h-full px-6 text-center mt-24 md:-mt-48 pb-20">
        <h2 className="my-2 lg:my-4 text-base ipad-mini:text-xl ipad:text-2xl lg:text-3xl max-w-2xl font-bold text-white/50">
          {subheadingText}
        </h2>
        <h1 className="text-lg ipad-mini:text-4xl ipad:text-5xl lg:text-7xl text-white font-bold drop-shadow-md">
          {headingText}
        </h1>
      </div>
    </section>
  );
};

export default HeroHomeSection;
