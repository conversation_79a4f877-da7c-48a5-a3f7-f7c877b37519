import Image from "next/image";

interface HeroBackgroundProps {
  isMobile: boolean;
}

const HeroBackground: React.FC<HeroBackgroundProps> = ({ isMobile }) => {
  return (
    <div className="absolute inset-0 z-0 h-full w-full overflow-hidden">
      <Image
        src={isMobile ? "/images/background/landing-pelajar.webp" : "/images/background/landing-pelajar.webp"}
        alt="Landing Background"
        fill
        className="object-cover object-top brightness-75 md:brightness-100"
        priority
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black via-black/90 to-transparent to-70% lg:to-20% " />
      <div className="absolute inset-0 bg-gradient-to-r from-black via-black/95 to-transparent to-50% lg:to-90% " />
    </div>
  );
};

export default HeroBackground;
