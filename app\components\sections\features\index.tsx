"use client";
import React from "react";
import Image from "next/image";

const FeaturesSection: React.FC = () => {
  const features = [
    {
      icon: "/images/accessories/class.svg",
      title: "<PERSON><PERSON><PERSON> kelas",
      subtitle: "seumur hidup",
      description:
        "Belajar itu gak pernah ada limitnya, di kelas GAMPAR AI ini kaka akan dapat akses belajar selamanya.",
    },
    {
      icon: "/images/accessories/update-class.svg",
      title: "Update materi",
      subtitle: "tiap minggu",
      description:
        "AI itu cepet banget updatenya, makadari itu kami akan selalu update Materi agar kaka tidak tertinggal perkembangan AI ini.",
    },
    {
      icon: "/images/accessories/community.svg",
      title: "Grup untuk",
      subtitle: "saling suport",
      description:
        "Punya komunitas dengan satu minat dan ketertarikan yang sama, itu asik banget. Apalagi jika saling suport di GAMPAR AI ini kaka akan mendapatkannya.",
    },
    {
      icon: "/images/accessories/question-and-answer.svg",
      title: "Live QnA",
      subtitle: "tiap minggu",
      description:
        "Sudah gabung kelas, eh malah ditinggalin sama mentornya, disini gak akan kisah seperti itu, karena kita akan rutin Zoom Meeting setiap minggu.",
    },
  ];

  return (
    <section className="z-30 w-full py-20 px-6 relative bg-black">
      <div className="ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto flex flex-col ipad-mini:flex-col md:flex-row gap-10 ipad-mini:pl-0 md:pl-20 justify-center">
        {/* Judul */}
        <h2 className="w-full ipad-mini:w-full md:w-1/3 text-white text-2xl lg:text-3xl text-left ipad-mini:text-center font-semibold tracking-wide pr-10">
          Semua ini cuma bisa <br />
          kaka dapatkan di kelas{" "}
          <span className="text-primaryVersion2">GAMPAR AI</span>
        </h2>

        {/* Fitur Grid */}
        <div className="w-full ipad-mini:w-full md:w-1/2 grid grid-cols-1 md:grid-cols-2 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="relative z-10 flex flex-col border border-dark-3 rounded-xl p-6 hover:border-primaryVersion2 transition"
            >
              <div className="w-10 h-10 mb-4 relative">
                <Image
                  src={feature.icon}
                  alt={`Icon ${index + 1}`}
                  fill
                  className="object-contain"
                />
              </div>
              <h4 className="text-white text-xl font-semibold capitalize">
                {feature.title}
              </h4>
              <h4 className="text-white text-xl font-semibold mb-4 capitalize">
                {feature.subtitle}
              </h4>
              
              <p className="text-white/50 text-base">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
