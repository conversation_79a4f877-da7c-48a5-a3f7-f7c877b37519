import Link from 'next/link'
import React from 'react'
import { FaWhatsapp } from 'react-icons/fa'

const CtaSection = () => {
  return (
    <section className="mx-6 lg:mx-auto ipad-mini:max-w-xl ipad:max-w-2xl lg:max-w-3xl my-16 text-center text-white">
      <h1 className='text-2xl lg:text-4xl font-bold ' >Ada Pertanyaan Lain?</h1>
      <div className='my-12'>
        <h1 className='text-xl lg:text-3xl font-light'>Gas Diskusi</h1>
        <h1 className='text-xl lg:text-3xl font-light'>Langsung aja, chat admin paling kece sejagat maya</h1>
      </div>
      <Link 
        href="https://wa.me/6289519442003?text=Halo%20admin%2C%20saya%20mau%20tanya%20tentang%20program%20Umum"
        target="_blank"
        rel="noopener noreferrer"
        className='inline-flex items-center gap-4 bg-green-600 px-6 py-3 text-white rounded-lg font-semibold hover:bg-green-700 transition mx-auto'>          <FaWhatsapp className="text-4xl" />
          <span className='text-xl'>Lanjut WHATSAPP</span>
      </Link>
    </section>
  )
}

export default CtaSection
