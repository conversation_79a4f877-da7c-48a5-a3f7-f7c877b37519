"use client";
import React from "react";
import Image from "next/image";
import { IoClose } from "react-icons/io5";


const reasons = [
  "Gak tau bagaimana cara bikinnya",
  "Gak tau cara nulis promptnya",
  "Gak tau pakai AI apa",
  "atau sudah coba bikin tapi hasilnya gak sesuai",
];


const ReasonItem = ({ text }: { text: string }) => (
  <span className="flex items-center gap-2 text-sm ipad-mini:text-sm ipad:text-xl md:text-xl">
    <IoClose className="w-4 h-4 ipad-mini:w-6 ipad-mini:h-6 ipad:w-8 ipad:h-8 lg:w-10 lg:h-10 text-red-500" />
    {text}
  </span>
);

const ReasonSection: React.FC = () => {
  return (
    <section className="w-full px-6 bg-black">
      <div className="flex flex-col ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto my-20 relative overflow-visible">
        <div className="flex flex-col-reverse md:flex-row items-center gap-8">
          <div className="w-full md:w-1/2 flex-col items-center justify-center">
            {/* <h2 className="block md:hidden text-2xl text-white font-bold text-center md:text-left">
              Ini Alasan Kenapa Kaka 
            </h2>
            <h2 className="block md:hidden text-2xl text-white font-bold text-center md:text-left mb-10">
              Perlu Belajar <span className="text-primaryVersion2">GAMPAR AI</span>
            </h2> */}
            <div className="w-full flex justify-center">
              <Image
                src="/images/accessories/kenapa.webp"
                alt="Kenapa MengGAMPAR AI"
                width={400}
                height={300}
              />
            </div>
          </div>

          <div className="w-full md:w-1/2 ipad:pr-0 md:pr-36 text-white md:text-left">
            {/* <div className="hidde wn md:block rounded-lg w-1/4 h-1 bg-primaryVersion2 md:left-[-7rem] mb-6"></div> */}
            {/* <h2 className="hidden md:block ipad-mini:text-2xl md:text-4xl text-white font-bold text-center md:text-left">
              Ini Alasan Kenapa Kaka
            </h2>
            <h2 className="hidden md:block ipad-mini:text-2xl md:text-4xl text-white font-bold text-center md:text-left mb-10">
              Perlu Belajar <span className="text-primaryVersion2">GAMPAR AI</span>
            </h2> */}
            <h4 className="text-sm ipad-mini:text-xl md:text-xl text-white font-bold md:text-left mb-10 tracking-wide">
              Kaka pasti pernah lihat konten keren pake AI, dan kaka pengen banget nyoba bikin yang sama seperti itu. tapi…  
            </h4>
            <p className="text-sm ipad-mini:text-xl lg:text-xl text-start font-light text-white/50 leading-relaxed">
              {reasons.map((reason, index) => (
                  <ReasonItem key={index} text={reason} />
                ))}
              <br />

                          
            </p>
            <p className="text-sm ipad-mini:text-xl lg:text-xl text-start font-light text-white/50 leading-relaxed tracking-widest">
              Kami faham kak, karena kami pernah ada diposisi itu
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ReasonSection;