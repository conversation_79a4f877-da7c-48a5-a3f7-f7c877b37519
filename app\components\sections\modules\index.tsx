"use client"
import { useState } from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";
import { IoPlayCircleOutline } from "react-icons/io5";

const modules = [
  {
    title: "Modul 1 - Tanpa menulis prompt",
    items: [
      "Cara 1",
      "Cara 2",
      "Cara 3",
      "Tips dapat prompt sangat sesuai wajah",
    ],
  },
  {
    title: "Modul 2 - Mengabaikan prompt",
    items: [
      "Faceswap",
      "Reference",
      "Restyle",
    ],
  },
  {
    title: "Modul 3 - Sempurnakan Gambar",
    items: [
      "Retouch",
      "Resize",
      "Upscale",
    ],
  },
  {
    title: "Modul 4 - Gambar jadi Video",
    items: [
      "Pakai AI gratisan / Hack",
      "Tanpa prompt vs Dengan prompt",
      "Rekomendasi AI video",
    ],
  },
  {
    title: "Modul 5 - Menjadi <PERSON>reatif",
    items: ["Trik bikin konten kreatif", "Gabung komunitas Gamvar A"],
  },
  {
    title: "Modul 6 - St<PERSON> kasus",
    items: [
      "Kupas tuntas cara membuat konten yang sedang viral",
      "Bedah tools AI",
    ],
  },
  {
    title: "Modul 7 – Monetisasi",
    items: ["Monetisasi digital dengan sosialmedia", "Sistem afiliasi"],
  },
];

const ModulesSection = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const toggleModule = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="w-full px-6 py-16 bg-black text-white">
      <div className="ipad-mini:max-w-xl ipad:max-w-2xl lg:max-w-3xl mx-auto">
        <h2 className="text-3xl font-bold text-center mb-8">
          Yang akan dipelajari
        </h2>

        <div className="space-y-4">
          {modules.map((modul, index) => {
            const isOpen = openIndex === index;

            return (
              <div key={index} className="bg-neutral-800 rounded-md overflow-hidden transition-all duration-300">
                <button
                  onClick={() => toggleModule(index)}
                  className={`w-full text-left p-4 flex justify-between items-center font-medium text-xs md:text-md lg:text-xl transition-colors duration-300 ${
                    isOpen ? "bg-yellow-500 text-black" : "bg-neutral-800 text-white"
                  }`}
                >
                  {modul.title}
                  {isOpen ? <FaChevronUp /> : <FaChevronDown />}
                </button>

                <div
                  className={`transition-all duration-500 ease-in-out overflow-hidden ${
                    isOpen ? "max-h-[500px] opacity-100 py-4 px-6" : "max-h-0 opacity-0 py-0 px-6"
                  }`}
                >
                  <ul className="space-y-3">
                    {modul.items.map((item, idx) => (
                      <li key={idx} className="flex items-center text-white text-xs md:text-md lg:text-xl ">
                        <IoPlayCircleOutline  className="mr-3 h-6 w-6 text-white" />
                        {item}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default ModulesSection;