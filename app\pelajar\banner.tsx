"use client";
import React from "react";
import Link from "next/link";
import Image from "next/image";

const WHATSAPP_LINK = "https://wa.me/6289519442003?text=Halo%20admin%2C%20saya%20mau%20tanya%20tentang%20program%20Pelajar";  

const BannerSections: React.FC = () => {
  return (
    <section className="w-full bg-black text-white py-12 px-4 md:py-20 md:px-6">
      <div className="max-w-7xl mx-auto">
        <div className="bg-dark-2 px-4 py-8 md:px-20 md:py-12 rounded-2xl flex flex-col md:flex-row items-center justify-between gap-8">
          <div className="text-center md:text-left w-full md:max-w-xs">
            <h2 className="text-2xl md:text-3xl font-bold mb-4">
              Yuk, <span className="text-primaryVersion2"><PERSON>ft<PERSON>an putra-putri <PERSON>a</span> <PERSON>
            </h2>
            <p className="text-sm md:text-xl text-white/80 mb-6">
              <PERSON>an tunggu putra-putri lain lebih dulu memahami teknologi yang
              akan membentuk masa depan.
            </p>
            <Link
              href={WHATSAPP_LINK}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center bg-primaryVersion2 hover:bg-primaryVersion2/80 px-6 py-3 rounded-lg font-semibold text-black text-base md:text-lg transition duration-300"
            >
              Daftar Sekarang
            </Link>
          </div>
          <Image
            src="/images/cards/section-1/th1.webp"
            alt="Daftar"
            width={500}
            height={500}
            className="rounded-xl aspect-video h-full max-w-xs md:max-w-md object-cover object-top mt-8 md:mt-0"
          />
        </div>
      </div>
    </section>
  );
};

export default BannerSections;