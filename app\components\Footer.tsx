import React from "react";
import Image from "next/image";
import { FaFacebook } from "react-icons/fa";
import { BsInstagram, BsTiktok, BsYoutube } from "react-icons/bs";
import Link from "next/link";
import { FaTiktok } from "react-icons/fa6";

const Footer: React.FC = () => {
  return (
    <section className="w-full text-white relative overflow-hidden">
      {/* Background */}
      <div className="absolute top-0 left-0 h-full w-0 md:w-full flex">
        <div className="bg-font w-1/2 h-full" />
        <div className="bg-primary w-1/2 h-full" />
      </div>

      {/* Main Content */}
      <div className="ipad-mini:max-w-2xl ipad:max-w-6xl lg:max-w-7xl mx-auto flex flex-col-reverse md:flex-row md:py-28 relative z-10">
        {/* Left Section */}
        <div className="flex flex-col items-center md:items-start  w-full md:w-1/2 space-y-4 px-8 md:pl-6 py-20 bg-font md:bg-transparent">
          {/* Logo */}
          <div>
            <Image
              src="/images/accessories/Aigensee-Logo-horizontal.png"
              alt="Aigensee Logo"
              width={300}
              height={120}
              className="object-contain"
            />
          </div>
          <hr className="w-full border-yellow-500 mb-6" />

          {/* Company Name */}
          <h3 className="text-lg font-light">Beranda Kami</h3>

          {/* Address */}
          <p className="text-xl md:text-2xl font-bold pb-4">Bandung, Jawa Barat</p>

          {/* Social Media */}
          <div className="flex gap-4 pb-4">
            <Link href="https://www.instagram.com/garisprtama" aria-label="Instagram" className="hover:text-primaryYellow">
              <div className="bg-white p-2 rounded-lg">
                <BsInstagram className="text-font" size={20} />
              </div>
            </Link>
            <Link href="https://www.facebook.com/garis.prtama" aria-label="Facebook" className="hover:text-primaryYellow">
              <div className="bg-white p-2 rounded-lg">
                <FaFacebook className="text-font" size={20} />
              </div>
            </Link>
            <Link href="https://youtube.com/@garisprtama" aria-label="YouTube" className="hover:text-primaryYellow">
              <div className="bg-white p-2 rounded-lg">
                <BsYoutube className="text-font" size={20} />
              </div>
            </Link>
            <Link href="https://tiktok.com/@garisprtama" aria-label="TikTok" className="hover:text-primaryYellow">
              <div className="bg-white p-2 rounded-lg">
                <FaTiktok className="text-font" size={20} />
              </div>
            </Link>
          </div>

          {/* Copyright */}
          <p className="text-md text-gray-300 mt-8">gamparai, © 2025</p>
        </div>

        {/* Right Section */}
        <div className="w-full md:w-1/2 flex flex-col items-center md:items-start text-center md:text-start gap-4 pl-px-6 md:pl-20 px-6 py-20 bg-primary md:bg-transparent">
    
          {/* Text Content */}
          <span className="text-2xl md:text-4xl lg:text-5xl font-bold leading-snug relative ">
            Teknologi terus berkembang
          {/* Line Decoration */}
          {/* <div className="absolute top-[-1rem] md:top-7 left-0 md:left-[-6rem] w-20 h-0 md:h-2 bg-white" /> */}
          </span>
          <p className="text-lg md:text-xl lg:text-2xl lg:text-2xl font-light">
            Yuk jadi bagian…
          </p>
          <p className="text-lg md:text-xl lg:text-2xl lg:text-2xl font-light">
            Dalam perkembangan teknologi yang luar biasa ini
          </p>
        </div>
      </div>
    </section>
  );
};

export default Footer;
